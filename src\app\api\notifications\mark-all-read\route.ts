import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function PATCH() {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all notifications that the user can see but hasn't read yet
    const { data: unreadNotifications, error: fetchError } = await supabase
      .from('notifications')
      .select(`
        id,
        notification_reads!left(user_id)
      `)
      .or(`recipient_id.eq.${user.id},recipient_id.is.null`)

    if (fetchError) {
      console.error('Error fetching notifications:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 })
    }

    // Filter to only unread notifications
    const unreadIds = unreadNotifications
      ?.filter(notification => {
        const userRead = notification.notification_reads?.find((read: { user_id: string }) => read.user_id === user.id)
        return !userRead
      })
      .map(notification => notification.id) || []

    if (unreadIds.length === 0) {
      return NextResponse.json({ success: true, message: 'No unread notifications' })
    }

    // Create read records for all unread notifications
    const readRecords = unreadIds.map(notificationId => ({
      notification_id: notificationId,
      user_id: user.id,
      read_at: new Date().toISOString()
    }))

    const { error: insertError } = await supabase
      .from('notification_reads')
      .upsert(readRecords, {
        onConflict: 'notification_id,user_id'
      })

    if (insertError) {
      console.error('Error marking notifications as read:', insertError)
      return NextResponse.json({ error: 'Failed to mark notifications as read' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `Marked ${unreadIds.length} notifications as read`
    })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
