import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const unreadOnly = searchParams.get('unread_only') === 'true'

    // Get notifications that are either:
    // 1. Addressed to this specific user (recipient_id = user.id)
    // 2. Broadcast notifications (recipient_id = null)
    const notificationsQuery = supabase
      .from('notifications')
      .select(`
        *,
        notification_reads!left(user_id, read_at)
      `)
      .or(`recipient_id.eq.${user.id},recipient_id.is.null`)
      .order('created_at', { ascending: false })
      .limit(limit)

    const { data: notifications, error } = await notificationsQuery

    if (error) {
      console.error('Error fetching notifications:', error)
      return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 })
    }

    // Process notifications to add read status for current user
    const processedNotifications = notifications?.map(notification => {
      const userRead = notification.notification_reads?.find((read: { user_id: string }) => read.user_id === user.id)
      return {
        ...notification,
        read_status: !!userRead,
        read_at: userRead?.read_at || null,
        notification_reads: undefined // Remove the join data from response
      }
    }) || []

    // Filter for unread only if requested
    const finalNotifications = unreadOnly
      ? processedNotifications.filter(n => !n.read_status)
      : processedNotifications

    return NextResponse.json({ notifications: finalNotifications })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is system admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (profileError || profile?.role !== 'system_admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const { title, message, type = 'info', recipient_id } = body

    if (!title || !message) {
      return NextResponse.json({ error: 'Title and message are required' }, { status: 400 })
    }

    const notificationData = {
      title,
      message,
      type,
      recipient_id: recipient_id === 'all' ? null : recipient_id,
      sender_id: user.id
    }

    if (recipient_id === 'all') {
      // Get total user count for broadcast notification
      const { count: userCount, error: countError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })

      if (countError) {
        console.error('Error counting users:', countError)
        return NextResponse.json({ error: 'Failed to count users' }, { status: 500 })
      }

      // Create a single broadcast notification (recipient_id = null)
      const broadcastNotification = {
        ...notificationData,
        recipient_id: null,
        total_recipients: userCount || 0
      }

      const { error: insertError } = await supabase
        .from('notifications')
        .insert(broadcastNotification)

      if (insertError) {
        console.error('Error inserting broadcast notification:', insertError)
        return NextResponse.json({ error: 'Failed to send notification' }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        message: `Broadcast notification sent to ${userCount} users`
      })
    } else {
      // Send to specific user
      const individualNotification = {
        ...notificationData,
        total_recipients: 1
      }

      const { error: insertError } = await supabase
        .from('notifications')
        .insert(individualNotification)

      if (insertError) {
        console.error('Error inserting notification:', insertError)
        return NextResponse.json({ error: 'Failed to send notification' }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        message: 'Notification sent successfully'
      })
    }
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
