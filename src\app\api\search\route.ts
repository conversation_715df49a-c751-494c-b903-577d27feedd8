import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { getUserProfile } from '@/lib/auth'
import { SearchResult, SearchResponse, SearchCategory } from '@/types/search'
import { UserRole } from '@/lib/supabase'

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Navigation items for search
const getNavigationItems = (role: UserRole): SearchResult[] => {
  const baseNavigation = [
    { id: 'nav-dashboard', title: 'الرئيسية', url: '/dashboard', type: 'navigation' as const },
    { id: 'nav-profile', title: 'الملف الشخصي', url: '/dashboard/profile', type: 'navigation' as const },
    { id: 'nav-calendar', title: 'التقويم', url: '/dashboard/calendar', type: 'navigation' as const },
  ]

  const roleSpecificNavigation: Record<UserRole, SearchResult[]> = {
    system_admin: [
      ...baseNavigation,
      { id: 'nav-users', title: 'إدارة المستخدمين', url: '/dashboard/users', type: 'navigation' as const },
      { id: 'nav-users-new', title: 'إضافة مستخدم', url: '/dashboard/users/new', type: 'navigation' as const },
      { id: 'nav-areas', title: 'إدارة المناطق', url: '/dashboard/areas', type: 'navigation' as const },
      { id: 'nav-teams', title: 'إدارة الفرق', url: '/dashboard/teams', type: 'navigation' as const },
      { id: 'nav-assign-users', title: 'تعيين المستخدمين', url: '/dashboard/assign-users', type: 'navigation' as const },
      { id: 'nav-packages', title: 'إدارة الباقات', url: '/dashboard/packages', type: 'navigation' as const },
      { id: 'nav-tickets', title: 'الطلبات', url: '/dashboard/tickets', type: 'navigation' as const },
      { id: 'nav-reports', title: 'التقارير', url: '/dashboard/reports', type: 'navigation' as const },
      { id: 'nav-roles', title: 'الأدوار والصلاحيات', url: '/dashboard/users/roles', type: 'navigation' as const },
    ],
    area_manager: [
      ...baseNavigation,
      { id: 'nav-teams', title: 'إدارة الفرق', url: '/dashboard/teams', type: 'navigation' as const },
      { id: 'nav-tickets', title: 'الطلبات', url: '/dashboard/tickets', type: 'navigation' as const },
      { id: 'nav-reports', title: 'التقارير', url: '/dashboard/reports', type: 'navigation' as const },
    ],
    team_manager: [
      ...baseNavigation,
      { id: 'nav-team', title: 'فريقي', url: '/dashboard/team', type: 'navigation' as const },
      { id: 'nav-team-members', title: 'أعضاء الفريق', url: '/dashboard/team/members', type: 'navigation' as const },
      { id: 'nav-tickets', title: 'الطلبات', url: '/dashboard/tickets', type: 'navigation' as const },
      { id: 'nav-reports', title: 'التقارير', url: '/dashboard/reports', type: 'navigation' as const },
    ],
    sales_employee: [
      ...baseNavigation,
      { id: 'nav-daily-closing', title: 'التقفيل اليومي', url: '/dashboard/daily-closing', type: 'navigation' as const },
      { id: 'nav-tickets', title: 'الطلبات', url: '/dashboard/tickets', type: 'navigation' as const },
    ],
  }

  return roleSpecificNavigation[role] || roleSpecificNavigation.sales_employee
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')?.trim()
    const type = searchParams.get('type') || 'all'
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 15) // Max 15 results

    console.log('Search API called with:', { query, type, limit })

    if (!query || query.length < 2) {
      return NextResponse.json({
        results: [],
        categories: {},
        total: 0,
        query: query || ''
      } as SearchResponse)
    }

    const profile = await getUserProfile()
    console.log('User profile:', profile?.role)

    if (!profile) {
      return NextResponse.json(
        { error: 'غير مصرح' },
        { status: 401 }
      )
    }

    const results: SearchResult[] = []
    const categories: Record<string, SearchCategory> = {}

    // Search navigation items
    if (type === 'all' || type === 'navigation') {
      const navItems = getNavigationItems(profile.role as UserRole)
      const filteredNavItems = navItems.filter(item =>
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        item.url.includes(query.toLowerCase())
      ).slice(0, 3) // Limit navigation items to 3

      if (filteredNavItems.length > 0) {
        categories.navigation = {
          name: 'navigation',
          arabicName: 'التنقل',
          results: filteredNavItems,
          icon: 'Navigation'
        }
        results.push(...filteredNavItems)
      }
    }

    // Search users based on role permissions
    if ((type === 'all' || type === 'user') && ['system_admin', 'area_manager', 'team_manager'].includes(profile.role)) {
      let userQuery = supabaseAdmin
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          role,
          area_id,
          team_id
        `)
        .or(`full_name.ilike.%${query}%,email.ilike.%${query}%`)
        .limit(Math.min(limit, 5)) // Max 5 users per search

      // Apply role-based filtering
      if (profile.role === 'area_manager' && profile.area_id) {
        userQuery = userQuery.eq('area_id', profile.area_id)
      } else if (profile.role === 'team_manager' && profile.team_id) {
        userQuery = userQuery.eq('team_id', profile.team_id)
      }

      const { data: users, error: usersError } = await userQuery

      if (usersError) {
        console.error('Error searching users:', usersError)
      } else if (users && users.length > 0) {
        const userResults: SearchResult[] = users.map(user => ({
          id: user.id,
          title: user.full_name || user.email,
          description: user.email,
          type: 'user' as const,
          url: `/dashboard/profile/${user.id}`,
          metadata: {
            role: user.role,
            email: user.email
          }
        }))

        categories.users = {
          name: 'users',
          arabicName: 'المستخدمين',
          results: userResults,
          icon: 'Users'
        }
        results.push(...userResults)
      }
    }

    // Search areas (system admin only)
    if ((type === 'all' || type === 'area') && profile.role === 'system_admin') {
      const { data: areas } = await supabaseAdmin
        .from('areas')
        .select(`
          id,
          name,
          description,
          manager_id
        `)
        .ilike('name', `%${query}%`)
        .limit(limit)

      if (areas && areas.length > 0) {
        const areaResults: SearchResult[] = areas.map(area => ({
          id: area.id,
          title: area.name,
          description: area.description || 'منطقة جغرافية',
          type: 'area' as const,
          url: `/dashboard/areas`,
          metadata: {}
        }))

        categories.areas = {
          name: 'areas',
          arabicName: 'المناطق',
          results: areaResults,
          icon: 'MapPin'
        }
        results.push(...areaResults)
      }
    }

    // Search teams based on role permissions
    if ((type === 'all' || type === 'team') && ['system_admin', 'area_manager'].includes(profile.role)) {
      let teamQuery = supabaseAdmin
        .from('teams')
        .select(`
          id,
          name,
          description,
          area_id,
          manager_id
        `)
        .ilike('name', `%${query}%`)
        .limit(limit)

      // Apply role-based filtering
      if (profile.role === 'area_manager' && profile.area_id) {
        teamQuery = teamQuery.eq('area_id', profile.area_id)
      }

      const { data: teams } = await teamQuery

      if (teams && teams.length > 0) {
        const teamResults: SearchResult[] = teams.map(team => ({
          id: team.id,
          title: team.name,
          description: team.description || 'فريق عمل',
          type: 'team' as const,
          url: `/dashboard/teams`,
          metadata: {}
        }))

        categories.teams = {
          name: 'teams',
          arabicName: 'الفرق',
          results: teamResults,
          icon: 'Users'
        }
        results.push(...teamResults)
      }
    }

    // Search tickets based on role permissions
    if (type === 'all' || type === 'ticket') {
      let ticketQuery = supabaseAdmin
        .from('tickets')
        .select(`
          id,
          title,
          description,
          status,
          priority,
          created_at,
          creator:profiles!tickets_created_by_fkey(full_name, email),
          assignee:profiles!tickets_assigned_to_fkey(full_name, email)
        `)
        .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
        .order('created_at', { ascending: false })
        .limit(limit)

      // Apply role-based filtering for tickets
      if (profile.role === 'sales_employee') {
        ticketQuery = ticketQuery.eq('created_by', profile.id);
      }
      // RLS policies handle role-based filtering automatically
      // No need for complex client-side filtering in search

      // System admin sees all tickets (no additional filtering)

      const { data: tickets } = await ticketQuery;

      if (tickets && tickets.length > 0) {
        const ticketResults: SearchResult[] = tickets.map(ticket => ({
          id: ticket.id,
          title: ticket.title,
          description: ticket.description?.substring(0, 100) + (ticket.description?.length > 100 ? '...' : ''),
          type: 'ticket' as const,
          url: `/dashboard/tickets/${ticket.id}`,
          metadata: {
            status: ticket.status,
            priority: ticket.priority,
            created_at: ticket.created_at
          }
        }))

        categories.tickets = {
          name: 'tickets',
          arabicName: 'الطلبات',
          results: ticketResults,
          icon: 'MessageSquare'
        }
        results.push(...ticketResults)
      }
    }

    // Search daily closings (sales employees only)
    if ((type === 'all' || type === 'daily_closing') && profile.role === 'sales_employee') {
      // Try to parse query as date or search in date strings
      const isDateQuery = /^\d{4}-\d{2}-\d{2}$/.test(query) || /^\d{2}\/\d{2}\/\d{4}$/.test(query)

      let closingQuery = supabaseAdmin
        .from('daily_closings')
        .select(`
          id,
          closing_date,
          attendance_submitted,
          sales_submitted,
          departure_submitted,
          total_sales_amount
        `)
        .eq('user_id', profile.id)
        .order('closing_date', { ascending: false })
        .limit(limit)

      if (isDateQuery) {
        // If query looks like a date, search by date
        closingQuery = closingQuery.gte('closing_date', query)
      } else {
        // Otherwise search in date strings (convert to text and search)
        closingQuery = closingQuery.ilike('closing_date', `%${query}%`)
      }

      const { data: dailyClosings } = await closingQuery

      if (dailyClosings && dailyClosings.length > 0) {
        const closingResults: SearchResult[] = dailyClosings.map(closing => ({
          id: closing.id,
          title: `التقفيل اليومي - ${closing.closing_date}`,
          description: `المبيعات: ${closing.total_sales_amount || 0} ريال`,
          type: 'daily_closing' as const,
          url: `/dashboard/daily-closing?date=${closing.closing_date}`,
          metadata: {
            status: closing.departure_submitted ? 'مكتمل' : 'غير مكتمل',
            created_at: closing.closing_date
          }
        }))

        categories.daily_closings = {
          name: 'daily_closings',
          arabicName: 'التقفيل اليومي',
          results: closingResults,
          icon: 'ClipboardCheck'
        }
        results.push(...closingResults)
      }
    }

    // Search packages (system admin only)
    if ((type === 'all' || type === 'package') && profile.role === 'system_admin') {
      const { data: packages } = await supabaseAdmin
        .from('packages')
        .select(`
          id,
          name,
          description,
          price,
          is_active
        `)
        .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
        .limit(limit)

      if (packages && packages.length > 0) {
        const packageResults: SearchResult[] = packages.map(pkg => ({
          id: pkg.id,
          title: pkg.name,
          description: `${pkg.description || ''} - ${pkg.price} ريال`,
          type: 'package' as const,
          url: `/dashboard/packages`,
          metadata: {
            status: pkg.is_active ? 'نشط' : 'غير نشط'
          }
        }))

        categories.packages = {
          name: 'packages',
          arabicName: 'الباقات',
          results: packageResults,
          icon: 'ShoppingCart'
        }
        results.push(...packageResults)
      }
    }

    return NextResponse.json({
      results: results.slice(0, limit),
      categories,
      total: results.length,
      query
    } as SearchResponse);

  } catch (error: unknown) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'حدث خطأ في البحث' },
      { status: 500 }
    );
  }
}
