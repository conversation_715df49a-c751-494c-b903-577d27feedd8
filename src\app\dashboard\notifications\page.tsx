import { requireAuth, getUserProfile } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { redirect } from 'next/navigation'
import { isSystemAdmin } from '@/lib/roles'
import { NotificationsManagement } from '@/components/admin/NotificationsManagement'

export default async function NotificationsPage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  // Check if user is system admin
  if (!profile || !isSystemAdmin(profile.role)) {
    redirect('/unauthorized')
  }

  return (
    <DashboardLayout user={{
      name: profile.full_name || user.email,
      email: user.email,
      role: profile.role
    }}>
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold">الإشعارات</h2>
          <p className="text-muted-foreground mt-2">
            إدارة وإرسال الإشعارات للمستخدمين
          </p>
        </div>

        <NotificationsManagement />
      </div>
    </DashboardLayout>
  )
}
