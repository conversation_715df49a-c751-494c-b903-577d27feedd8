// Types and constants for the ticket system

export interface TicketWithDetails {
  id: string
  title: string
  description: string
  status: 'open' | 'in_progress' | 'closed'
  priority: 'low' | 'medium' | 'high'
  created_by: string
  assigned_to: string | null
  created_at: string
  updated_at: string
  creator?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
  assignee?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
  reply_count?: number
}

export interface TicketReply {
  id: string
  ticket_id: string
  user_id: string
  message: string
  created_at: string
  user?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
}

// Role hierarchy
export const ROLE_LEVELS = {
  'system_admin': 1,
  'area_manager': 2,
  'team_manager': 3,
  'sales_employee': 4
} as const

export type UserRole = keyof typeof ROLE_LEVELS
