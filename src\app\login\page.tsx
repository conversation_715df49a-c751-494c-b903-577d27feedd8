'use client'

import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useAuthSync } from '@/hooks/useAuthSync'
import { useNavigation } from '@/hooks/useNavigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingPage } from '@/components/ui/loading'
import { Eye, EyeOff } from 'lucide-react'
import { toast } from '@/hooks/use-toast'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [, setError] = useState('')
  const [submitting, setSubmitting] = useState(false)
  const redirectAttempted = useRef(false)

  const { signIn, loading, isAuthenticated } = useAuth()
  const { navigateWithAuth } = useNavigation()
  const { refreshAuth } = useAuthSync()

  // Redirect if already authenticated
  useEffect(() => {
    if (!loading && isAuthenticated && !redirectAttempted.current) {
      redirectAttempted.current = true
      console.log('Login page: Redirecting authenticated user to /dashboard')
      navigateWithAuth('/dashboard', { replace: true })
    }
  }, [loading, isAuthenticated, navigateWithAuth])

  // Show loading page while checking authentication
  if (loading) {
    return <LoadingPage />
  }

  // Don't render login form if already authenticated
  if (isAuthenticated) {
    return <LoadingPage />
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setSubmitting(true)

    try {
      const { error } = await signIn(email, password)

      if (error) {
        toast.error('البريد الإلكتروني أو كلمة المرور غير صحيحة')
        setSubmitting(false)
        return
      }

      toast.success('تم تسجيل الدخول بنجاح')

      // Wait a moment for auth state to update, then refresh if needed
      setTimeout(() => {
        if (!isAuthenticated) {
          console.log('Auth state not updated after login, refreshing...')
          refreshAuth()
        }
        setSubmitting(false)
      }, 1000)

      // Redirect will be handled by useEffect when auth state changes
    } catch (err) {
      console.error('Login error:', err)
      toast.error('حدث خطأ أثناء تسجيل الدخول')
      setSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center space-y-4">
          <CardTitle className="text-2xl font-bold text-primary">
            سحابة المدينة
          </CardTitle>
          <CardDescription className="text-base">
            تسجيل الدخول إلى النظام
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            
            <div className="space-y-2">
              <Label htmlFor="email">البريد الإلكتروني</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="أدخل البريد الإلكتروني"
                required
                disabled={submitting}
                className="text-right"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">كلمة المرور</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="أدخل كلمة المرور"
                  required
                  disabled={submitting}
                  className="text-right pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  disabled={submitting}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
            </div>
            
            <Button
              type="submit"
              className="w-full"
              disabled={submitting}
            >
              {submitting ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
