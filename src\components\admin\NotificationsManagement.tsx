'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Bell, Send, Users, User, Info, AlertTriangle,
  Search, Trash2, CheckSquare, XCircle, Filter,
  Building2, UserCheck
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { createClient } from '@/lib/supabase/client'

interface User {
  id: string
  full_name: string | null
  email: string
  role: string
  area_id?: string
  team_id?: string
  area?: {
    id: string
    name: string
  }
  team?: {
    id: string
    name: string
  }
}

interface Area {
  id: string
  name: string
}

interface Team {
  id: string
  name: string
  area_id: string
}

interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'warning' | 'error'
  recipient_id: string | null
  sender_id: string
  total_recipients: number
  created_at: string
  updated_at: string
  recipient?: {
    full_name: string | null
    email: string
  }
  read_count?: number
}

const notificationTypes = [
  { value: 'info', label: 'معلومة', icon: Info, color: 'bg-blue-500' },
  { value: 'warning', label: 'تنبيه', icon: AlertTriangle, color: 'bg-yellow-500' },
  { value: 'error', label: 'تحذير', icon: XCircle, color: 'bg-red-500' },
]

export function NotificationsManagement() {
  const [users, setUsers] = useState<User[]>([])
  const [areas, setAreas] = useState<Area[]>([])
  const [teams, setTeams] = useState<Team[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(false)
  const [sending, setSending] = useState(false)
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [deleting, setDeleting] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [filters, setFilters] = useState({
    area: '',
    team: '',
    role: ''
  })
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: 'info' as const,
    recipient_type: 'all' as 'all' | 'selected' | 'area' | 'team'
  })
  const { toast } = useToast()
  const supabase = createClient()

  // Load users and notifications
  useEffect(() => {
    loadUsers()
    loadAreas()
    loadTeams()
    loadNotifications()

    // Set up real-time subscription for read status updates
    const channel = supabase
      .channel('admin_notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notification_reads',
        },
        () => {
          // Reload notifications when someone reads a notification
          loadNotifications()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [])

  const loadUsers = async () => {
    try {
      // First try to load users with area and team relationships
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          role,
          area_id,
          team_id
        `)
        .order('full_name')

      if (error) throw error

      // If successful, try to enrich with area and team data
      if (data) {
        // Try to get area names for users with area_id
        const usersWithAreas = await Promise.all(
          data.map(async (user) => {
            const enrichedUser: typeof user & { area?: { id: string; name: string }; team?: { id: string; name: string } } = { ...user }

            // Try to get area info if area_id exists
            if (user.area_id) {
              try {
                const { data: areaData } = await supabase
                  .from('areas')
                  .select('id, name')
                  .eq('id', user.area_id)
                  .single()

                if (areaData) {
                  enrichedUser.area = areaData
                }
              } catch {
                // Ignore area lookup errors
              }
            }

            // Try to get team info if team_id exists
            if (user.team_id) {
              try {
                const { data: teamData } = await supabase
                  .from('teams')
                  .select('id, name')
                  .eq('id', user.team_id)
                  .single()

                if (teamData) {
                  enrichedUser.team = teamData
                }
              } catch {
                // Ignore team lookup errors
              }
            }

            return enrichedUser
          })
        )

        setUsers(usersWithAreas)
      } else {
        setUsers([])
      }
    } catch (error) {
      console.error('Error loading users:', error)
      toast.error('فشل في تحميل المستخدمين')
      // Fallback: try to load just basic user data
      try {
        const { data: basicData, error: basicError } = await supabase
          .from('profiles')
          .select('id, full_name, email, role')
          .order('full_name')

        if (!basicError && basicData) {
          setUsers(basicData)
        }
      } catch (fallbackError) {
        console.error('Fallback user loading also failed:', fallbackError)
      }
    }
  }

  const loadAreas = async () => {
    try {
      const { data, error } = await supabase
        .from('areas')
        .select('id, name')
        .order('name')

      if (error) {
        // If areas table doesn't exist or has issues, just set empty array
        console.log('Areas table not available:', error.message)
        setAreas([])
        return
      }
      setAreas(data || [])
    } catch (error) {
      console.log('Error loading areas:', error)
      setAreas([])
    }
  }

  const loadTeams = async () => {
    try {
      const { data, error } = await supabase
        .from('teams')
        .select('id, name, area_id')
        .order('name')

      if (error) {
        // If teams table doesn't exist or has issues, just set empty array
        console.log('Teams table not available:', error.message)
        setTeams([])
        return
      }
      setTeams(data || [])
    } catch (error) {
      console.log('Error loading teams:', error)
      setTeams([])
    }
  }

  const loadNotifications = async () => {
    setLoading(true)
    try {
      const { data: currentUser } = await supabase.auth.getUser()
      if (!currentUser.user) throw new Error('User not authenticated')

      // First, get the notifications sent by this user
      const { data: notificationsData, error: notificationsError } = await supabase
        .from('notifications')
        .select(`
          *,
          recipient:profiles!notifications_recipient_id_fkey(full_name, email)
        `)
        .eq('sender_id', currentUser.user.id)
        .order('created_at', { ascending: false })
        .limit(50)

      if (notificationsError) throw notificationsError

      // Get read counts for each notification
      const notificationIds = notificationsData?.map(n => n.id) || []

      let readCounts: { [key: string]: number } = {}

      if (notificationIds.length > 0) {
        const { data: readData, error: readError } = await supabase
          .from('notification_reads')
          .select('notification_id')
          .in('notification_id', notificationIds)

        if (readError) throw readError

        // Count reads per notification
        readCounts = (readData || []).reduce((acc, read) => {
          acc[read.notification_id] = (acc[read.notification_id] || 0) + 1
          return acc
        }, {} as { [key: string]: number })
      }

      // Process notifications to add read statistics
      const processedNotifications = (notificationsData || []).map(notification => {
        const readCount = readCounts[notification.id] || 0

        return {
          ...notification,
          read_count: readCount,
          // Use the stored total_recipients from the database
          total_recipients: notification.total_recipients
        }
      })

      setNotifications(processedNotifications)
    } catch (error) {
      console.error('Error loading notifications:', error)
      toast.error('فشل في تحميل الإشعارات')
    } finally {
      setLoading(false)
    }
  }

  const sendNotification = async () => {
    if (!formData.title.trim() || !formData.message.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة')
      return
    }

    if (formData.recipient_type === 'selected' && selectedUsers.length === 0) {
      toast.error('يرجى اختيار مستخدمين لإرسال الإشعار إليهم')
      return
    }

    if (formData.recipient_type === 'area' && !filters.area) {
      toast.error('يرجى اختيار منطقة لإرسال الإشعار إليها')
      return
    }

    if (formData.recipient_type === 'team' && !filters.team) {
      toast.error('يرجى اختيار فريق لإرسال الإشعار إليه')
      return
    }

    setSending(true)
    try {
      const { data: currentUser } = await supabase.auth.getUser()
      if (!currentUser.user) throw new Error('User not authenticated')

      if (formData.recipient_type === 'all') {
        // Create a single broadcast notification
        const { error } = await supabase
          .from('notifications')
          .insert({
            title: formData.title,
            message: formData.message,
            type: formData.type,
            recipient_id: null, // null for broadcast
            sender_id: currentUser.user.id
          })

        if (error) throw error
        toast.success(`تم إرسال الإشعار إلى جميع المستخدمين`)
      } else if (formData.recipient_type === 'area') {
        // Send to all users in selected area
        const areaUsers = users.filter(user => user.area_id === filters.area)
        const notifications = areaUsers.map(user => ({
          title: formData.title,
          message: formData.message,
          type: formData.type,
          recipient_id: user.id,
          sender_id: currentUser.user.id
        }))

        const { error } = await supabase
          .from('notifications')
          .insert(notifications)

        if (error) throw error
        const areaName = areas.find(a => a.id === filters.area)?.name
        toast.success(`تم إرسال الإشعار إلى منطقة ${areaName} (${areaUsers.length} مستخدم)`)
      } else if (formData.recipient_type === 'team') {
        // Send to all users in selected team
        const teamUsers = users.filter(user => user.team_id === filters.team)
        const notifications = teamUsers.map(user => ({
          title: formData.title,
          message: formData.message,
          type: formData.type,
          recipient_id: user.id,
          sender_id: currentUser.user.id
        }))

        const { error } = await supabase
          .from('notifications')
          .insert(notifications)

        if (error) throw error
        const teamName = teams.find(t => t.id === filters.team)?.name
        toast.success(`تم إرسال الإشعار إلى فريق ${teamName} (${teamUsers.length} مستخدم)`)
      } else {
        // Send to selected users
        const notifications = selectedUsers.map(userId => ({
          title: formData.title,
          message: formData.message,
          type: formData.type,
          recipient_id: userId,
          sender_id: currentUser.user.id
        }))

        const { error } = await supabase
          .from('notifications')
          .insert(notifications)

        if (error) throw error
        toast.success(`تم إرسال الإشعار إلى ${selectedUsers.length} مستخدم`)
      }

      // Reset form
      setFormData({
        title: '',
        message: '',
        type: 'info',
        recipient_type: 'all'
      })
      setSelectedUsers([])
      setFilters({ area: '', team: '', role: '' })

      // Reload notifications
      loadNotifications()
    } catch (error) {
      console.error('Error sending notification:', error)
      toast.error('فشل في إرسال الإشعار')
    } finally {
      setSending(false)
    }
  }

  const getTypeIcon = (type: string) => {
    const typeConfig = notificationTypes.find(t => t.value === type)
    if (!typeConfig) return Info
    return typeConfig.icon
  }

  const getTypeColor = (type: string) => {
    const typeConfig = notificationTypes.find(t => t.value === type)
    return typeConfig?.color || 'bg-blue-500'
  }

  const getRoleInArabic = (role: string) => {
    const roleMap: { [key: string]: string } = {
      'system_admin': 'مدير النظام',
      'area_manager': 'مدير منطقة',
      'team_manager': 'مدير فريق',
      'sales_employee': 'موظف مبيعات'
    }
    return roleMap[role] || role
  }

  const getRoleColor = (role: string) => {
    const colorMap: { [key: string]: string } = {
      'system_admin': 'bg-purple-100 text-purple-800 border-purple-200',
      'area_manager': 'bg-blue-100 text-blue-800 border-blue-200',
      'team_manager': 'bg-green-100 text-green-800 border-green-200',
      'sales_employee': 'bg-orange-100 text-orange-800 border-orange-200'
    }
    return colorMap[role] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  // Multi-select functions for notifications
  const handleSelectAllNotifications = (checked: boolean) => {
    if (checked) {
      setSelectedNotifications(notifications.map(n => n.id))
    } else {
      setSelectedNotifications([])
    }
  }

  const handleNotificationSelect = (notificationId: string, checked: boolean) => {
    if (checked) {
      setSelectedNotifications(prev => [...prev, notificationId])
    } else {
      setSelectedNotifications(prev => prev.filter(id => id !== notificationId))
    }
  }

  // Delete selected notifications
  const deleteSelectedNotifications = async () => {
    if (selectedNotifications.length === 0) return

    setDeleting(true)
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .in('id', selectedNotifications)

      if (error) throw error

      toast.success(`تم حذف ${selectedNotifications.length} إشعار بنجاح`)
      setSelectedNotifications([])
      loadNotifications()
    } catch (error) {
      console.error('Error deleting notifications:', error)
      toast.error('فشل في حذف الإشعارات')
    } finally {
      setDeleting(false)
    }
  }

  // User selection functions
  const handleUserSelect = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId])
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId))
    }
  }

  const handleSelectAllUsers = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(filteredUsers.map(u => u.id))
    } else {
      setSelectedUsers([])
    }
  }

  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    // Search filter
    const matchesSearch = user.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase())

    // Area filter
    const matchesArea = !filters.area || user.area_id === filters.area

    // Team filter
    const matchesTeam = !filters.team || user.team_id === filters.team

    // Role filter
    const matchesRole = !filters.role || user.role === filters.role

    return matchesSearch && matchesArea && matchesTeam && matchesRole
  })

  return (
    <div className="space-y-6">
      {/* Send Notification Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            إرسال إشعار جديد
          </CardTitle>
          <CardDescription>
            إرسال إشعار إلى مستخدم محدد أو جميع المستخدمين
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">عنوان الإشعار</Label>
              <Input
                id="title"
                placeholder="أدخل عنوان الإشعار"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">نوع الإشعار</Label>
              <Select
                value={formData.type}
                onValueChange={(value: 'info') => setFormData(prev => ({ ...prev, type: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {notificationTypes.map(type => {
                    const Icon = type.icon
                    return (
                      <SelectItem key={type.value} value={type.value}>
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          {type.label}
                        </div>
                      </SelectItem>
                    )
                  })}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="recipient">المستلم</Label>
            <Select
              value={formData.recipient_type}
              onValueChange={(value: 'all' | 'selected' | 'area' | 'team') => {
                setFormData(prev => ({ ...prev, recipient_type: value }))
                if (value === 'all') {
                  setSelectedUsers([])
                  setFilters({ area: '', team: '', role: '' })
                }
              }}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    جميع المستخدمين
                  </div>
                </SelectItem>
                <SelectItem value="area">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4" />
                    منطقة محددة
                  </div>
                </SelectItem>
                <SelectItem value="team">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    فريق محدد
                  </div>
                </SelectItem>
                <SelectItem value="selected">
                  <div className="flex items-center gap-2">
                    <CheckSquare className="h-4 w-4" />
                    مستخدمين محددين
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Area Selection */}
          {formData.recipient_type === 'area' && (
            <div className="space-y-4 border rounded-lg p-4 bg-muted/20">
              <Label className="text-sm font-medium">اختيار المنطقة</Label>
              <Select
                value={filters.area}
                onValueChange={(value) => setFilters(prev => ({ ...prev, area: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر المنطقة" />
                </SelectTrigger>
                <SelectContent>
                  {areas.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4" />
                        {area.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {filters.area && (
                <Badge variant="secondary">
                  {users.filter(u => u.area_id === filters.area).length} مستخدم في هذه المنطقة
                </Badge>
              )}
            </div>
          )}

          {/* Team Selection */}
          {formData.recipient_type === 'team' && (
            <div className="space-y-4 border rounded-lg p-4 bg-muted/20">
              <Label className="text-sm font-medium">اختيار الفريق</Label>
              <Select
                value={filters.team}
                onValueChange={(value) => setFilters(prev => ({ ...prev, team: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر الفريق" />
                </SelectTrigger>
                <SelectContent>
                  {teams.map(team => (
                    <SelectItem key={team.id} value={team.id}>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        {team.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {filters.team && (
                <Badge variant="secondary">
                  {users.filter(u => u.team_id === filters.team).length} مستخدم في هذا الفريق
                </Badge>
              )}
            </div>
          )}

          {/* Enhanced User Selection */}
          {formData.recipient_type === 'selected' && (
            <div className="space-y-4 border rounded-lg p-4 bg-muted/20">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">اختيار المستخدمين</Label>
                <Badge variant="secondary">
                  {selectedUsers.length} من {filteredUsers.length} محدد
                </Badge>
              </div>

              {/* Search Users */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="ابحث عن المستخدمين..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 text-right"
                  dir="rtl"
                />
              </div>

              {/* Filters */}
              <div className="grid grid-cols-3 gap-2">
                <Select
                  value={filters.area || "all"}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, area: value === "all" ? "" : value }))}
                >
                  <SelectTrigger className="text-sm">
                    <SelectValue placeholder="المنطقة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع المناطق</SelectItem>
                    {areas.map(area => (
                      <SelectItem key={area.id} value={area.id}>
                        <div className="flex items-center gap-2">
                          <Filter className="h-4 w-4" />
                          {area.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={filters.team || "all"}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, team: value === "all" ? "" : value }))}
                >
                  <SelectTrigger className="text-sm">
                    <SelectValue placeholder="الفريق" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الفرق</SelectItem>
                    {teams.map(team => (
                      <SelectItem key={team.id} value={team.id}>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          {team.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={filters.role || "all"}
                  onValueChange={(value) => setFilters(prev => ({ ...prev, role: value === "all" ? "" : value }))}
                >
                  <SelectTrigger className="text-sm">
                    <SelectValue placeholder="الدور" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأدوار</SelectItem>
                    <SelectItem value="system_admin">مدير النظام</SelectItem>
                    <SelectItem value="area_manager">مدير منطقة</SelectItem>
                    <SelectItem value="team_manager">مدير فريق</SelectItem>
                    <SelectItem value="sales_employee">موظف مبيعات</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Select All Checkbox */}
              <div className="flex items-center gap-2">
                <Checkbox
                  id="select-all-users"
                  checked={filteredUsers.length > 0 && filteredUsers.every(user => selectedUsers.includes(user.id))}
                  onCheckedChange={handleSelectAllUsers}
                />
                <Label htmlFor="select-all-users" className="text-sm cursor-pointer">
                  تحديد الكل ({filteredUsers.length} مستخدم)
                </Label>
              </div>

              {/* Enhanced Users Table */}
              <div className="border rounded-md bg-background">
                {filteredUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">لا توجد مستخدمين</p>
                  </div>
                ) : (
                  <div className="max-h-64 overflow-y-auto">
                    {/* Table Header */}
                    <div className="sticky top-0 bg-muted/50 border-b px-3 py-2">
                      <div className="grid grid-cols-12 gap-2 text-xs font-medium text-muted-foreground">
                        <div className="col-span-1 text-center">تحديد</div>
                        <div className="col-span-4 text-right">الاسم</div>
                        <div className="col-span-3 text-center">الدور</div>
                        <div className="col-span-2 text-center">المنطقة</div>
                        <div className="col-span-2 text-center">الفريق</div>
                      </div>
                    </div>

                    {/* Table Body */}
                    <div className="divide-y">
                      {filteredUsers.map(user => (
                        <div key={user.id} className="px-3 py-2 hover:bg-muted/30 transition-colors">
                          <div className="grid grid-cols-12 gap-2 items-center">
                            {/* Checkbox */}
                            <div className="col-span-1 flex justify-center">
                              <Checkbox
                                id={`user-${user.id}`}
                                checked={selectedUsers.includes(user.id)}
                                onCheckedChange={(checked) => handleUserSelect(user.id, checked as boolean)}
                              />
                            </div>

                            {/* Name */}
                            <div className="col-span-4">
                              <Label htmlFor={`user-${user.id}`} className="cursor-pointer">
                                <div className="flex items-center gap-2 text-right">
                                  <User className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                  <div className="min-w-0">
                                    <div className="text-sm font-medium truncate">
                                      {user.full_name || 'غير محدد'}
                                    </div>
                                    <div className="text-xs text-muted-foreground truncate">
                                      {user.email}
                                    </div>
                                  </div>
                                </div>
                              </Label>
                            </div>

                            {/* Role */}
                            <div className="col-span-3 flex justify-center">
                              <Badge
                                variant="outline"
                                className={`text-xs border ${getRoleColor(user.role)}`}
                              >
                                <UserCheck className="h-3 w-3 ml-1" />
                                {getRoleInArabic(user.role)}
                              </Badge>
                            </div>

                            {/* Area */}
                            <div className="col-span-2 flex justify-center">
                              {user.area ? (
                                <Badge variant="secondary" className="text-xs">
                                  <Building2 className="h-3 w-3 ml-1" />
                                  {user.area.name}
                                </Badge>
                              ) : (
                                <span className="text-xs text-muted-foreground">غير محدد</span>
                              )}
                            </div>

                            {/* Team */}
                            <div className="col-span-2 flex justify-center">
                              {user.team ? (
                                <Badge variant="secondary" className="text-xs">
                                  <Users className="h-3 w-3 ml-1" />
                                  {user.team.name}
                                </Badge>
                              ) : (
                                <span className="text-xs text-muted-foreground">غير محدد</span>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="message">محتوى الإشعار</Label>
            <Textarea
              id="message"
              placeholder="أدخل محتوى الإشعار"
              rows={4}
              value={formData.message}
              onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
            />
          </div>

          <Button 
            onClick={sendNotification} 
            disabled={sending}
            className="w-full"
          >
            {sending ? 'جاري الإرسال...' : 'إرسال الإشعار'}
          </Button>
        </CardContent>
      </Card>

      <Separator />

      {/* Sent Notifications */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                الإشعارات المرسلة
              </CardTitle>
              <CardDescription>
                عرض آخر الإشعارات التي تم إرسالها
              </CardDescription>
            </div>

            {/* Bulk Actions */}
            {selectedNotifications.length > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {selectedNotifications.length} محدد
                </Badge>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={deleteSelectedNotifications}
                  disabled={deleting}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  {deleting ? 'جاري الحذف...' : 'حذف المحدد'}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">جاري التحميل...</p>
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-8">
              <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">لا توجد إشعارات مرسلة</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Select All Header */}
              <div className="flex items-center gap-2 pb-2 border-b">
                <Checkbox
                  checked={notifications.length > 0 && notifications.every(n => selectedNotifications.includes(n.id))}
                  onCheckedChange={handleSelectAllNotifications}
                />
                <Label className="text-sm font-medium cursor-pointer">
                  تحديد الكل ({notifications.length} إشعار)
                </Label>
              </div>

              {/* Notifications */}
              {notifications.map(notification => {
                const Icon = getTypeIcon(notification.type)
                const isSelected = selectedNotifications.includes(notification.id)

                return (
                  <div
                    key={notification.id}
                    className={`border rounded-lg p-4 space-y-2 transition-colors ${
                      isSelected ? 'bg-primary/5 border-primary/20' : ''
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => handleNotificationSelect(notification.id, checked as boolean)}
                        className="mt-1"
                      />

                      <div className="flex-1 space-y-2">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2">
                            <div className={`p-1 rounded-full ${getTypeColor(notification.type)}`}>
                              <Icon className="h-3 w-3 text-white" />
                            </div>
                            <h4 className="font-medium">{notification.title}</h4>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">
                              {notification.read_count || 0} / {notification.total_recipients} قرأوا
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {new Date(notification.created_at).toLocaleDateString('ar-SA')}
                            </span>
                          </div>
                        </div>

                        <p className="text-sm text-muted-foreground">{notification.message}</p>

                        <div className="text-xs text-muted-foreground">
                          إلى: {notification.recipient_id ?
                            (notification.recipient?.full_name || notification.recipient?.email) :
                            `جميع المستخدمين (${notification.total_recipients} مستخدم)`
                          }
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
