'use client'

import { useEffect, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useAuth } from '@/hooks/useAuth'

/**
 * Component to handle session refresh and prevent stale auth state
 */
export function SessionRefresh() {
  const { user, loading } = useAuth()
  const supabase = createClient()
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastRefreshRef = useRef<number>(0)

  useEffect(() => {
    // Don't start refresh cycle if still loading or no user
    if (loading || !user) {
      return
    }

    const startRefreshCycle = () => {
      // Clear any existing interval
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }

      // Refresh session every 5 minutes
      refreshIntervalRef.current = setInterval(async () => {
        const now = Date.now()
        
        // Prevent too frequent refreshes
        if (now - lastRefreshRef.current < 60000) { // 1 minute minimum
          return
        }

        try {
          console.log('Refreshing session...')
          const { error } = await supabase.auth.refreshSession()
          
          if (error) {
            console.error('Session refresh error:', error)
          } else {
            console.log('Session refreshed successfully')
            lastRefreshRef.current = now
          }
        } catch (error) {
          console.error('Session refresh failed:', error)
        }
      }, 5 * 60 * 1000) // 5 minutes
    }

    startRefreshCycle()

    // Also refresh on page visibility change
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const now = Date.now()
        // If page was hidden for more than 5 minutes, refresh immediately
        if (now - lastRefreshRef.current > 5 * 60 * 1000) {
          console.log('Page became visible after long absence, refreshing session...')
          supabase.auth.refreshSession()
          lastRefreshRef.current = now
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current)
      }
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [user, loading, supabase.auth])

  // This component doesn't render anything
  return null
}
