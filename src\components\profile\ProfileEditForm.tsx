'use client'

import { useState } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { UserAvatar } from '@/components/ui/user-avatar'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ArrowLeft, Mail, Phone, Calendar, User, Shield, Save, X, MapPin, Users } from 'lucide-react'
import { UserRole, RoleLevel } from '@/lib/supabase'
import { getRoleArabicName, getRoleBadgeVariant } from '@/lib/roles'

type Profile = {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: UserRole
  role_level: RoleLevel
  created_at: string
  updated_at: string
  team?: {
    id: string
    name: string
    area?: {
      id: string
      name: string
    }
  } | null
  area?: {
    id: string
    name: string
  } | null
}

interface ProfileEditFormProps {
  profile: Profile
}

export function ProfileEditForm({ profile }: ProfileEditFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const [editForm, setEditForm] = useState({
    full_name: profile.full_name || '',
    phone: profile.phone || ''
  })

  const handleUpdateProfile = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/profile/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          full_name: editForm.full_name,
          phone: editForm.phone
        })
      })

      const result = await response.json()

      if (!response.ok) {
        setError('خطأ في تحديث الملف الشخصي: ' + result.error)
        return
      }

      setSuccess('تم تحديث الملف الشخصي بنجاح')
      
      // Clear success message after 2 seconds and redirect
      setTimeout(() => {
        setSuccess('')
        router.push('/dashboard/profile')
      }, 2000)
      
    } catch {
      setError('حدث خطأ غير متوقع')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    // Use a consistent format to avoid hydration mismatches
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'UTC' // Use UTC to ensure consistency between server and client
    })
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 ml-2" />
            العودة
          </Button>
          <div>
            <h2 className="text-3xl font-bold">تعديل الملف الشخصي</h2>
            <p className="text-muted-foreground mt-2">
              تعديل معلوماتك الشخصية
            </p>
          </div>
        </div>
      </div>

      {/* Success/Error Messages */}
      {(success || error) && (
        <div dir="rtl">
          {error && (
            <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md mb-2 text-right">
              {error}
            </div>
          )}
          {success && (
            <div className="text-sm text-green-800 bg-green-100 p-3 rounded-md text-right">
              {success}
            </div>
          )}
        </div>
      )}

      {/* User Profile Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start gap-6">
            <UserAvatar
              src=""
              alt={profile.full_name || 'مستخدم'}
              name={profile.full_name}
              size="xl"
            />
            
            <div className="flex-1 space-y-2">
              <div className="flex items-center gap-3">
                <h3 className="text-2xl font-bold">
                  {profile.full_name || 'غير محدد'}
                </h3>
                <Badge variant={getRoleBadgeVariant(profile.role)}>
                  {getRoleArabicName(profile.role)}
                </Badge>
              </div>
              
              <div className="flex items-center gap-2 text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span>{profile.email}</span>
              </div>
              
              {profile.phone && (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  <span>{profile.phone}</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Edit Form */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              تعديل المعلومات الأساسية
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit_full_name" className="text-right block">الاسم الكامل</Label>
              <Input
                id="edit_full_name"
                value={editForm.full_name}
                onChange={(e) => setEditForm({ ...editForm, full_name: e.target.value })}
                placeholder="أدخل الاسم الكامل"
                className="text-right"
                dir="rtl"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_phone" className="text-right block">رقم الهاتف</Label>
              <Input
                id="edit_phone"
                type="tel"
                value={editForm.phone}
                onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
                placeholder="أدخل رقم الهاتف"
                className="text-right"
                dir="rtl"
              />
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">البريد الإلكتروني</Label>
              <p className="text-sm font-medium text-muted-foreground">{profile.email} (لا يمكن تعديله)</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">الدور</Label>
              <div className="flex items-center gap-2 mt-1">
                {profile.role === 'system_admin' ? (
                  <Shield className="h-4 w-4 text-blue-600" />
                ) : (
                  <User className="h-4 w-4 text-gray-600" />
                )}
                <span className="text-sm font-medium text-muted-foreground">
                  {getRoleArabicName(profile.role)} (لا يمكن تعديله)
                </span>
              </div>
            </div>

            {/* Team Information */}
            {profile.team && (
              <>
                <Separator />
                <div>
                  <Label className="text-sm text-muted-foreground">الفريق</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Users className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium text-muted-foreground">{profile.team.name}</span>
                  </div>
                </div>
              </>
            )}

            {/* Area Information */}
            {(profile.area || profile.team?.area) && (
              <>
                <Separator />
                <div>
                  <Label className="text-sm text-muted-foreground">المنطقة</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <MapPin className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium text-muted-foreground">
                      {profile.area?.name || profile.team?.area?.name}
                    </span>
                  </div>
                </div>
              </>
            )}

            <div className="flex justify-start gap-2 pt-4" dir="rtl">
              <Button onClick={handleUpdateProfile} disabled={loading}>
                <Save className="h-4 w-4 ml-2" />
                {loading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </Button>
              <Button variant="outline" onClick={() => router.back()}>
                <X className="h-4 w-4 ml-2" />
                إلغاء
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              معلومات الحساب
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm text-muted-foreground">تاريخ إنشاء الحساب</Label>
              <p className="text-sm font-medium">{formatDate(profile.created_at)}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">آخر تحديث</Label>
              <p className="text-sm font-medium">{formatDate(profile.updated_at)}</p>
            </div>

            <Separator />

            <div>
              <Label className="text-sm text-muted-foreground">معرف المستخدم</Label>
              <p className="text-xs font-mono bg-muted p-2 rounded">{profile.id}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
