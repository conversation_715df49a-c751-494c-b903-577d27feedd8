'use client'

import Link from 'next/link'
import { UserAvatar } from '@/components/ui/user-avatar'
import { Badge } from '@/components/ui/badge'
import { getRoleArabicName, getRoleBadgeVariant } from '@/lib/roles'
import { UserRole } from '@/lib/supabase'
import { cn } from '@/lib/utils'

interface ProfileLinkProps {
  userId: string
  name: string | null
  email: string
  role?: string
  showRole?: boolean
  showAvatar?: boolean
  avatarSize?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  textClassName?: string
  asLink?: boolean
}

export function ProfileLink({
  userId,
  name,
  email,
  role,
  showRole = false,
  showAvatar = true,
  avatarSize = 'sm',
  className,
  textClassName,
  asLink = true
}: ProfileLinkProps) {
  const displayName = name || email
  
  const content = (
    <div className={cn('flex items-center gap-2', className)}>
      {showAvatar && (
        <UserAvatar
          name={displayName}
          size={avatarSize}
        />
      )}
      <div className="flex flex-col">
        <span className={cn(
          'font-medium',
          asLink && 'text-blue-600 hover:text-blue-800 cursor-pointer',
          textClassName
        )}>
          {displayName}
        </span>
        {showRole && role && (
          <Badge variant={getRoleBadgeVariant(role as UserRole)} className="text-xs w-fit">
            {getRoleArabicName(role as UserRole)}
          </Badge>
        )}
      </div>
    </div>
  )

  if (asLink) {
    return (
      <Link href={`/dashboard/profile/${userId}`} className="inline-block">
        {content}
      </Link>
    )
  }

  return content
}

// Simplified version for just displaying a name as a link
interface ProfileNameLinkProps {
  userId: string
  name: string | null
  email: string
  className?: string
}

export function ProfileNameLink({ userId, name, email, className }: ProfileNameLinkProps) {
  const displayName = name || email
  
  return (
    <Link 
      href={`/dashboard/profile/${userId}`} 
      className={cn(
        'text-blue-600 hover:text-blue-800 cursor-pointer font-medium',
        className
      )}
    >
      {displayName}
    </Link>
  )
}
