'use client'

import { useRef, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Search } from 'lucide-react'
import { useSearch } from '@/hooks/useSearch'
import { SearchResults } from './SearchResults'
import { cn } from '@/lib/utils'

interface SearchBarProps {
  className?: string
  placeholder?: string
  showMobileButton?: boolean
}

export function SearchBar({ 
  className, 
  placeholder = "البحث...",
  showMobileButton = false 
}: SearchBarProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const {
    query,
    setQuery,
    results,
    categories,
    loading,
    error,
    // total, // Unused
    isOpen,
    setIsOpen,
    clearSearch,
    selectedIndex,
    handleKeyDown
  } = useSearch({
    debounceMs: 300,
    minQueryLength: 2,
    limit: 20
  })

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Ctrl+K or Cmd+K to focus search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        inputRef.current?.focus()
      }
    }

    document.addEventListener('keydown', handleGlobalKeyDown)
    return () => document.removeEventListener('keydown', handleGlobalKeyDown)
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value)
  }

  const handleInputFocus = () => {
    if (query.length >= 2) {
      setIsOpen(true)
    }
  }

  const handleInputBlur = (e: React.FocusEvent) => {
    // Only close if not clicking on the popover content
    setTimeout(() => {
      if (!e.relatedTarget?.closest('[data-radix-popper-content-wrapper]')) {
        setIsOpen(false)
      }
    }, 100)
  }

  // const handleClearSearch = () => {
  //   clearSearch()
  //   inputRef.current?.focus()
  // } // Unused

  const handleResultClick = () => {
    setIsOpen(false)
    clearSearch()
  }

  return (
    <>
      {/* Desktop Search */}
      <div className={cn("relative hidden md:block", className)}>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <div className="relative w-[200px] lg:w-[300px]">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                ref={inputRef}
                type="search"
                placeholder={placeholder}
                value={query}
                onChange={handleInputChange}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
                onKeyDown={handleKeyDown}
                className="w-full pr-4 pl-10 text-right"
                dir="rtl"
              />
            </div>
          </PopoverTrigger>
          
          <PopoverContent
            className="p-0 max-h-64 overflow-hidden"
            align="start"
            side="bottom"
            sideOffset={4}
            onOpenAutoFocus={(e) => e.preventDefault()}
            style={{ width: 'var(--radix-popover-trigger-width)' }}
          >
            <SearchResults
              categories={categories}
              results={results}
              loading={loading}
              error={error}
              query={query}
              selectedIndex={selectedIndex}
              onResultClick={handleResultClick}
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Mobile Search Button */}
      {showMobileButton && (
        <div className="md:hidden">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon">
                <Search className="h-4 w-4" />
                <span className="sr-only">البحث</span>
              </Button>
            </PopoverTrigger>
            
            <PopoverContent
              className="w-[90vw] max-w-[320px] p-0 max-h-64 overflow-hidden"
              align="end"
              side="bottom"
              sideOffset={8}
              onOpenAutoFocus={(e) => e.preventDefault()}
            >
              <div className="p-4 border-b">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder={placeholder}
                    value={query}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                    className="pr-4 pl-10 text-right"
                    dir="rtl"
                    autoFocus
                  />
                </div>
              </div>
              
              <SearchResults
                categories={categories}
                results={results}
                loading={loading}
                error={error}
                query={query}
                selectedIndex={selectedIndex}
                onResultClick={handleResultClick}
                className="border-0 shadow-none"
              />
            </PopoverContent>
          </Popover>
        </div>
      )}
    </>
  )
}
