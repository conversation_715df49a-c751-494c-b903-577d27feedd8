'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs' // Unused
import { SearchBar } from './SearchBar'
import { useSearch } from '@/hooks/useSearch'
import { SearchResults } from './SearchResults'
import { SearchType } from '@/types/search'
import { Search, Users, MapPin, MessageSquare, ShoppingCart, ClipboardCheck, Navigation } from 'lucide-react'

interface SearchDemoProps {
  userRole: string
}

export function SearchDemo({ userRole }: SearchDemoProps) {
  const [selectedType, setSelectedType] = useState<SearchType>('all')

  const search = useSearch({
    type: selectedType,
    limit: 10
  })

  const searchTypes = [
    { value: 'all', label: 'الكل', icon: Search },
    { value: 'navigation', label: 'التنقل', icon: Navigation },
    { value: 'user', label: 'المستخدمين', icon: Users },
    { value: 'area', label: 'المناطق', icon: MapPin },
    { value: 'team', label: 'الفرق', icon: Users },
    { value: 'ticket', label: 'الطلبات', icon: MessageSquare },
    { value: 'package', label: 'الباقات', icon: ShoppingCart },
    { value: 'daily_closing', label: 'التقفيل اليومي', icon: ClipboardCheck },
  ]

  const availableTypes = searchTypes.filter(type => {
    switch (userRole) {
      case 'system_admin':
        return true // All types available
      case 'area_manager':
        return !['package', 'daily_closing'].includes(type.value)
      case 'team_manager':
        return !['area', 'package', 'daily_closing'].includes(type.value)
      case 'sales_employee':
        return ['all', 'navigation', 'ticket', 'daily_closing'].includes(type.value)
      default:
        return ['all', 'navigation'].includes(type.value)
    }
  })

  const exampleQueries = {
    navigation: ['الرئيسية', 'المستخدمين', 'التقارير'],
    user: ['محمد', 'admin', 'مدير'],
    area: ['الرياض', 'جدة', 'الدمام'],
    team: ['فريق', 'مبيعات', 'تسويق'],
    ticket: ['مشكلة', 'طلب', 'استفسار'],
    package: ['باقة', 'خدمة', 'منتج'],
    daily_closing: ['2024', 'اليوم', 'أمس']
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Search Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            واجهة البحث التفاعلية
          </CardTitle>
          <CardDescription>
            جرب البحث في النظام باستخدام أنواع مختلفة من الاستعلامات
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Type Selector */}
          <div>
            <h4 className="font-medium mb-3">نوع البحث</h4>
            <div className="flex flex-wrap gap-2">
              {availableTypes.map((type) => {
                const Icon = type.icon
                return (
                  <Button
                    key={type.value}
                    variant={selectedType === type.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedType(type.value as SearchType)}
                    className="flex items-center gap-2"
                  >
                    <Icon className="h-4 w-4" />
                    {type.label}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* Search Bar */}
          <div>
            <h4 className="font-medium mb-3">شريط البحث</h4>
            <SearchBar placeholder={`البحث في ${availableTypes.find(t => t.value === selectedType)?.label || 'النظام'}...`} />
          </div>

          {/* Example Queries */}
          {exampleQueries[selectedType as keyof typeof exampleQueries] && (
            <div>
              <h4 className="font-medium mb-3">أمثلة للبحث</h4>
              <div className="flex flex-wrap gap-2">
                {exampleQueries[selectedType as keyof typeof exampleQueries].map((query) => (
                  <Badge
                    key={query}
                    variant="secondary"
                    className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                    onClick={() => search.setQuery(query)}
                  >
                    {query}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Search Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            نتائج البحث
          </CardTitle>
          <CardDescription>
            النتائج المباشرة للبحث مع التصنيف حسب النوع
          </CardDescription>
        </CardHeader>
        <CardContent>
          {search.query ? (
            <SearchResults
              categories={search.categories}
              results={search.results}
              loading={search.loading}
              error={search.error}
              query={search.query}
              selectedIndex={search.selectedIndex}
              onResultClick={() => {}}
              className="border-0 shadow-none"
            />
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>ابدأ بكتابة في شريط البحث أعلاه لرؤية النتائج</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Role Information */}
      <Card>
        <CardHeader>
          <CardTitle>معلومات الدور الحالي</CardTitle>
          <CardDescription>
            الصلاحيات والميزات المتاحة لدورك الحالي
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Badge variant="default">{userRole}</Badge>
              <span className="text-sm text-muted-foreground">
                {userRole === 'system_admin' && 'مدير النظام - صلاحيات كاملة'}
                {userRole === 'area_manager' && 'مدير منطقة - إدارة المنطقة والفرق'}
                {userRole === 'team_manager' && 'مدير فريق - إدارة الفريق'}
                {userRole === 'sales_employee' && 'موظف مبيعات - البيانات الشخصية'}
              </span>
            </div>

            <div>
              <h4 className="font-medium mb-2">الفئات المتاحة للبحث:</h4>
              <div className="flex flex-wrap gap-2">
                {availableTypes.map((type) => {
                  const Icon = type.icon
                  return (
                    <Badge key={type.value} variant="outline" className="flex items-center gap-1">
                      <Icon className="h-3 w-3" />
                      {type.label}
                    </Badge>
                  )
                })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>تعليمات الاستخدام</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-center gap-2">
              <Badge variant="outline">Ctrl+K</Badge>
              <span>للتركيز على شريط البحث</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">↑/↓</Badge>
              <span>للتنقل بين النتائج</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">Enter</Badge>
              <span>لفتح النتيجة المحددة</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">Escape</Badge>
              <span>لإغلاق نتائج البحث</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
