'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Plus, MessageSquare, Clock, User } from 'lucide-react'
import { TicketStatusBadge } from './TicketStatusBadge'
import { TicketPriorityBadge } from './TicketPriorityBadge'
import { formatDistanceToNow } from 'date-fns'
import { ar } from 'date-fns/locale'
import { getTickets } from '@/app/dashboard/tickets/actions'
import { CardLoader, InlineLoading } from '@/components/ui/loading'
import { DataErrorBoundary } from '@/components/ui/error-boundary'
// import { useSupabaseQuery } from '@/hooks/useSupabaseQuery' // Unused

interface Ticket {
  id: string
  title: string
  description: string
  status: 'open' | 'in_progress' | 'closed'
  priority: 'low' | 'medium' | 'high'
  created_by: string
  assigned_to: string | null
  created_at: string
  updated_at: string
  creator?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
  assignee?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
  reply_count?: number
}

interface TicketListProps {
  userRole: string
  userId: string
  statusFilter?: 'all' | 'open' | 'in_progress' | 'closed' | 'open_and_progress'
  showCreateButton?: boolean
}

export function TicketList({ userRole, userId, statusFilter = 'all', showCreateButton = true }: TicketListProps) {
  const router = useRouter()
  const [tickets, setTickets] = useState<Ticket[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')



  useEffect(() => {
    fetchTickets()
  }, [statusFilter])

  const fetchTickets = async () => {
    const timeoutId = setTimeout(() => {
      console.warn('Ticket fetch timeout - stopping loading')
      setLoading(false)
      setError('انتهت مهلة تحميل الطلبات. يرجى المحاولة مرة أخرى.')
    }, 8000) // Reduced timeout to 8 seconds

    try {
      setLoading(true)
      setError('')

      const result = await getTickets(statusFilter)

      clearTimeout(timeoutId)

      if (result.error) {
        console.error('Server action returned error:', result.error)
        setError(result.error)
        return
      }

      setTickets(result.data || [])
    } catch (error: unknown) {
      clearTimeout(timeoutId)
      console.error('Error fetching tickets:', error)
      setError(`حدث خطأ في تحميل الطلبات: ${error instanceof Error ? error.message : 'خطأ غير متوقع'}`)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold">الطلبات</h2>
            <InlineLoading text="جاري تحميل الطلبات..." />
          </div>
          {showCreateButton && (
            <Button asChild>
              <Link href="/dashboard/tickets/new" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                طلب جديد
              </Link>
            </Button>
          )}
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <CardLoader key={i} />
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive mb-4">{error}</p>
        <Button onClick={fetchTickets} variant="outline">
          إعادة المحاولة
        </Button>
      </div>
    )
  }

  return (
    <DataErrorBoundary>
      <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">الطلبات</h2>
          <p className="text-muted-foreground mt-2">
            {statusFilter === 'all' ? 'جميع الطلبات' :
             statusFilter === 'open' ? 'الطلبات المفتوحة' :
             statusFilter === 'open_and_progress' ? 'الطلبات النشطة (المفتوحة وقيد المعالجة)' :
             statusFilter === 'in_progress' ? 'الطلبات قيد المعالجة' :
             'الطلبات المغلقة'}
          </p>
        </div>
        {showCreateButton && (
          <Button asChild>
            <Link href="/dashboard/tickets/new" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              طلب جديد
            </Link>
          </Button>
        )}
      </div>

      {/* Tickets List */}
      {tickets.length === 0 ? (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">لا توجد طلبات</h3>
              <p className="text-muted-foreground mb-4">
                {statusFilter === 'all' ? 'لم يتم إنشاء أي طلبات بعد' :
                 statusFilter === 'open' ? 'لا توجد طلبات مفتوحة حالياً' :
                 statusFilter === 'open_and_progress' ? 'لا توجد طلبات نشطة حالياً' :
                 statusFilter === 'in_progress' ? 'لا توجد طلبات قيد المعالجة حالياً' :
                 'لا توجد طلبات مغلقة'}
              </p>
              {showCreateButton && (
                <Button asChild>
                  <Link href="/dashboard/tickets/new">
                    إنشاء طلب جديد
                  </Link>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {tickets.map((ticket) => (
            <Link key={ticket.id} href={`/dashboard/tickets/${ticket.id}`} className="ticket-card-link block group">
              <Card className={`ticket-card hover:shadow-lg hover:border-primary/20 transition-all duration-200 border-l-4 cursor-pointer ${
                (userRole === 'team_manager' || userRole === 'area_manager' || userRole === 'system_admin')
                  ? (ticket.created_by === userId
                      ? 'border-l-blue-400 bg-blue-50/30' // My tickets - blue
                      : 'border-l-green-400 bg-green-50/30') // Tickets to me - green
                  : 'border-l-primary/30' // Default for sales employees
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg mb-2 hover:text-primary transition-colors group-hover:underline">
                        {ticket.title}
                      </CardTitle>
                      <CardDescription className="line-clamp-2 text-sm leading-relaxed">
                        {ticket.description}
                      </CardDescription>

                      {/* Context for managers */}
                      {(userRole === 'team_manager' || userRole === 'area_manager' || userRole === 'system_admin') && (
                        <div className="mt-2 text-xs text-muted-foreground">
                          {ticket.created_by === userId ? (
                            <span className="text-blue-600">
                              طلبك - في انتظار رد {userRole === 'team_manager' ? 'مدير المنطقة' : userRole === 'area_manager' ? 'مدير النظام' : 'الإدارة'}
                            </span>
                          ) : (
                            <span className="text-green-600">
                              طلب مُوجه إليك - يحتاج ردك
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col gap-2 mr-4 items-end">
                      <TicketStatusBadge status={ticket.status} />
                      <TicketPriorityBadge priority={ticket.priority} />
                    </div>
                  </div>
                </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-4 text-muted-foreground">
                    {/* Direction indicator for managers */}
                    {(userRole === 'team_manager' || userRole === 'area_manager' || userRole === 'system_admin') && (
                      <div className="flex items-center gap-2">
                        {ticket.created_by === userId ? (
                          <div className="flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 rounded-md text-xs font-medium">
                            <span>↗</span>
                            <span>طلبي</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 px-2 py-1 bg-green-50 text-green-700 rounded-md text-xs font-medium">
                            <span>↙</span>
                            <span>مُوجه لي</span>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      {userRole === 'sales_employee' ? (
                        ticket.assignee ? (
                          <span
                            onClick={(e) => {
                              e.preventDefault()
                              e.stopPropagation()
                              router.push(`/dashboard/profile/${ticket.assignee?.id}`)
                            }}
                            className="text-primary hover:text-primary/80 transition-colors cursor-pointer font-medium"
                          >
                            {ticket.assignee?.full_name || ticket.assignee?.email}
                          </span>
                        ) : (
                          <span className="font-medium">مدير الفريق</span>
                        )
                      ) : (
                        ticket.creator ? (
                          <span
                            onClick={(e) => {
                              e.preventDefault()
                              e.stopPropagation()
                              router.push(`/dashboard/profile/${ticket.creator?.id}`)
                            }}
                            className="text-primary hover:text-primary/80 transition-colors cursor-pointer font-medium"
                          >
                            {ticket.creator?.full_name || ticket.creator?.email}
                          </span>
                        ) : (
                          <span className="font-medium">موظف مبيعات</span>
                        )
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      <span>
                        {formatDistanceToNow(new Date(ticket.created_at), {
                          addSuffix: true,
                          locale: ar
                        })}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    {(ticket.reply_count || 0) > 0 && (
                      <Badge variant="outline" className="text-xs bg-blue-100 border-blue-300 text-blue-800 hover:bg-blue-200">
                        <MessageSquare className="h-3 w-3 ml-1" />
                        {ticket.reply_count} رد
                      </Badge>
                    )}
                    <span className="text-primary hover:text-primary/80 font-medium text-xs transition-colors">
                      عرض التفاصيل ←
                    </span>
                  </div>
                </div>
              </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}
      </div>
    </DataErrorBoundary>
  )
}
