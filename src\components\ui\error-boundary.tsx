'use client'

import React from 'react'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'
import { Button } from './button'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ error, errorInfo })
    this.props.onError?.(error, errorInfo)
    console.error('Error caught by boundary:', error, errorInfo)
  }

  retry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback
      if (FallbackComponent && this.state.error) {
        return <FallbackComponent error={this.state.error} retry={this.retry} />
      }
      
      return <DefaultErrorFallback error={this.state.error} retry={this.retry} />
    }

    return this.props.children
  }
}

interface ErrorFallbackProps {
  error?: Error
  retry: () => void
}

function DefaultErrorFallback({ error, retry }: ErrorFallbackProps) {
  return (
    <div className="min-h-[400px] flex items-center justify-center p-8">
      <div className="text-center space-y-6 max-w-md">
        <div className="flex justify-center">
          <div className="rounded-full bg-destructive/10 p-4">
            <AlertTriangle className="h-8 w-8 text-destructive" />
          </div>
        </div>
        
        <div className="space-y-2">
          <h2 className="text-xl font-semibold text-foreground">
            حدث خطأ غير متوقع
          </h2>
          <p className="text-muted-foreground">
            نعتذر، حدث خطأ أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى.
          </p>
        </div>

        {error && process.env.NODE_ENV === 'development' && (
          <details className="text-left bg-muted p-4 rounded-lg text-sm">
            <summary className="cursor-pointer font-medium mb-2">تفاصيل الخطأ</summary>
            <pre className="whitespace-pre-wrap text-xs">{error.message}</pre>
          </details>
        )}

        <div className="flex gap-3 justify-center">
          <Button onClick={retry} variant="default" className="gap-2">
            <RefreshCw className="h-4 w-4" />
            إعادة المحاولة
          </Button>
          <Button 
            onClick={() => window.location.href = '/dashboard'} 
            variant="outline" 
            className="gap-2"
          >
            <Home className="h-4 w-4" />
            العودة للرئيسية
          </Button>
        </div>
      </div>
    </div>
  )
}

// Navigation-specific error boundary
export function NavigationErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={({ retry }) => (
        <div className="p-4 text-center space-y-4">
          <div className="text-destructive text-sm">
            خطأ في التنقل
          </div>
          <Button onClick={retry} size="sm" variant="outline">
            إعادة المحاولة
          </Button>
        </div>
      )}
      onError={(error) => {
        console.error('Navigation error:', error)
      }}
    >
      {children}
    </ErrorBoundary>
  )
}

// Data loading error boundary
export function DataErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      fallback={({ retry }) => (
        <div className="p-6 text-center space-y-4 border border-destructive/20 rounded-lg bg-destructive/5">
          <AlertTriangle className="h-6 w-6 text-destructive mx-auto" />
          <div>
            <h3 className="font-medium text-destructive">فشل في تحميل البيانات</h3>
            <p className="text-sm text-muted-foreground mt-1">
              حدث خطأ أثناء تحميل البيانات. يرجى المحاولة مرة أخرى.
            </p>
          </div>
          <Button onClick={retry} size="sm" variant="outline">
            إعادة التحميل
          </Button>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  )
}
