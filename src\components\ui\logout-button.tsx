'use client'

import { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useLoading } from '@/components/providers/LoadingProvider'
import { Button } from './button'
import { LogOut } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LogoutButtonProps {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
  className?: string
  showIcon?: boolean
  children?: React.ReactNode
}

export function LogoutButton({
  variant = 'outline',
  size = 'sm',
  className,
  showIcon = true,
  children
}: LogoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { signOut } = useAuth()
  const { withLoading } = useLoading()

  const handleSignOut = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (isLoading) return // Prevent double clicks

    try {
      await withLoading(
        new Promise<void>(async (resolve) => {
          try {
            setIsLoading(true)
            
            if (!signOut) {
              console.error('signOut function not available')
              // Force redirect as fallback
              window.location.href = '/login'
              resolve()
              return
            }

            console.log('Starting logout process...')
            const result = await signOut()

            if (result && result.error) {
              console.error('Sign out error (continuing with redirect):', result.error)
            }

            // Force redirect after a short delay
            setTimeout(() => {
              window.location.href = '/login'
              resolve()
            }, 500)

          } catch (error) {
            console.error('Logout error (forcing redirect):', error)
            // Force redirect even on error
            window.location.href = '/login'
            resolve()
          }
        }),
        'جاري تسجيل الخروج...'
      )
    } catch (error) {
      console.error('Logout wrapper error:', error)
      // Final fallback
      window.location.href = '/login'
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleSignOut}
      disabled={isLoading}
      className={cn(className)}
    >
      {showIcon && <LogOut className="h-4 w-4 ml-2" />}
      {children || (isLoading ? 'جاري تسجيل الخروج...' : 'تسجيل الخروج')}
    </Button>
  )
}
