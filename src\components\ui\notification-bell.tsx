'use client'

import { useState } from 'react'
import { Bell, Check, XCircle, Info, AlertTriangle, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
// import { ScrollArea } from '@/components/ui/scroll-area' // Unused
import { useNotifications } from '@/hooks/useNotifications'
import { cn } from '@/lib/utils'

const notificationIcons = {
  info: Info,
  warning: AlertTriangle,
  error: XCircle,
}

const notificationColors = {
  info: 'text-blue-500',
  warning: 'text-yellow-500',
  error: 'text-red-500',
}

export function NotificationBell() {
  const [isOpen, setIsOpen] = useState(false)
  const { notifications, unreadCount, loading, markAsRead, markAllAsRead } = useNotifications(10)

  const handleNotificationClick = async (id: string, isRead: boolean) => {
    if (!isRead) {
      await markAsRead(id)
    }
  }

  const handleMarkAllRead = async () => {
    await markAllAsRead()
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'الآن'
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `منذ ${diffInDays} يوم`
    
    return date.toLocaleDateString('ar-SA')
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "relative hover:cursor-pointer transition-all duration-200",
            "hover:bg-accent/50 hover:scale-105 active:scale-95",
            "focus-visible:ring-2 focus-visible:ring-primary/20"
          )}
          aria-label="الإشعارات"
        >
          <Bell className={cn(
            "h-4 w-4 transition-colors duration-200",
            unreadCount > 0 ? "text-primary" : "text-muted-foreground"
          )} />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className={cn(
                "absolute -top-1 -right-1 h-5 w-5 rounded-full p-0",
                "flex items-center justify-center text-[10px] font-medium",
                "shadow-lg border-2 border-background",
                "animate-bounce"
              )}
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent
        align="end"
        className={cn(
          "w-80 max-h-96 shadow-xl border-0",
          "bg-card/95 backdrop-blur-sm",
          "rounded-xl p-0 overflow-hidden"
        )}
        side="bottom"
        sideOffset={8}
      >
        <div className="bg-gradient-to-r from-primary/5 to-primary/10 px-4 py-3 border-b border-border/50">
          <DropdownMenuLabel className="flex items-center justify-between text-right p-0" dir="rtl">
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-full bg-primary/10">
                <Bell className="h-3.5 w-3.5 text-primary" />
              </div>
              <span className="font-semibold text-foreground">الإشعارات</span>
              {unreadCount > 0 && (
                <Badge variant="secondary" className="text-[10px] px-1.5 py-0.5">
                  {unreadCount} جديد
                </Badge>
              )}
            </div>
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMarkAllRead}
                className={cn(
                  "h-auto px-2 py-1 text-xs hover:cursor-pointer",
                  "text-primary hover:text-white hover:bg-accent",
                  "rounded-md transition-colors duration-200"
                )}
              >
                <Check className="h-3 w-3 ml-1" />
                تحديد الكل كمقروء
              </Button>
            )}
          </DropdownMenuLabel>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2 text-muted-foreground">
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-primary/20 border-t-primary"></div>
              <span className="text-sm">جاري التحميل...</span>
            </div>
          </div>
        ) : notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
            <div className="p-4 rounded-full bg-muted/30 mb-4">
              <Bell className="h-8 w-8 text-muted-foreground/50" />
            </div>
            <p className="text-sm font-medium text-foreground mb-1">لا توجد إشعارات</p>
            <p className="text-xs text-muted-foreground leading-relaxed">
              ستظهر الإشعارات الجديدة هنا عند وصولها
            </p>
          </div>
        ) : (
          <div className="max-h-64 overflow-y-auto">
            <div className="p-1">
              {notifications.map((notification) => {
                const Icon = notificationIcons[notification.type] || Info
                const iconColor = notificationColors[notification.type] || 'text-blue-500'

                return (
                  <DropdownMenuItem
                    key={notification.id}
                    className={cn(
                      "flex flex-col items-start gap-0 p-0 cursor-pointer text-right",
                      "rounded-lg mb-1 transition-all duration-200",
                      "hover:bg-accent/10 hover:shadow-sm active:scale-[0.98]",
                      "focus:bg-accent/20 focus:outline-none",
                      !notification.read_status && "bg-primary/5 border border-primary/10"
                    )}
                    onClick={() => handleNotificationClick(notification.id, notification.read_status)}
                  >
                    <div className="flex items-start gap-3 w-full p-3" dir="rtl">
                      {/* Unread indicator */}
                      <div className="flex-shrink-0 mt-1">
                        {!notification.read_status ? (
                          <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                        ) : (
                          <div className="w-2 h-2" />
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0 text-right">
                        <div className="flex items-center justify-between gap-2 mb-1">
                          <div className={cn(
                            "flex-shrink-0 p-1.5 rounded-full transition-colors duration-200",
                            !notification.read_status ? "bg-primary/10" : "bg-muted/50"
                          )}>
                            <Icon className={cn("h-3.5 w-3.5", iconColor)} />
                          </div>
                          <h4 className={cn(
                            "text-sm truncate flex-1 text-right transition-colors duration-200",
                            !notification.read_status
                              ? "font-semibold text-foreground"
                              : "font-medium text-muted-foreground"
                          )}>
                            {notification.title}
                          </h4>
                        </div>

                        <p className={cn(
                          "text-xs line-clamp-2 text-right leading-relaxed mb-2",
                          !notification.read_status
                            ? "text-foreground/80"
                            : "text-muted-foreground"
                        )}>
                          {notification.message}
                        </p>

                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatTimeAgo(notification.created_at)}</span>
                          </div>
                          {!notification.read_status && (
                            <Badge variant="secondary" className="text-[10px] px-1.5 py-0.5">
                              جديد
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </DropdownMenuItem>
                )
              })}
            </div>
          </div>
        )}
        

      </DropdownMenuContent>
    </DropdownMenu>
  )
}
