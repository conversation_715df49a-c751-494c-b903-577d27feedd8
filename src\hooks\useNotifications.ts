'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useToast } from '@/hooks/use-toast'
import type { RealtimeChannel } from '@supabase/supabase-js'

interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'warning' | 'error'
  recipient_id: string | null
  sender_id: string
  total_recipients: number
  read_status: boolean // This will be computed from notification_reads
  read_at: string | null
  created_at: string
  updated_at: string
}

interface UseNotificationsReturn {
  notifications: Notification[]
  unreadCount: number
  loading: boolean
  error: string | null
  markAsRead: (id: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  refreshNotifications: () => Promise<void>
}

export function useNotifications(limit: number = 10): UseNotificationsReturn {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()
  const supabase = createClient()

  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/notifications?limit=${limit}`)
      if (!response.ok) {
        throw new Error('Failed to fetch notifications')
      }

      const data = await response.json()
      const notifications = data.notifications || []

      setNotifications(notifications)
      setUnreadCount(notifications.filter((n: Notification) => !n.read_status).length)
    } catch (err) {
      console.error('Error fetching notifications:', err)
      setError('Failed to fetch notifications')
    } finally {
      setLoading(false)
    }
  }, [limit])

  const markAsRead = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}/read`, {
        method: 'PATCH',
      })

      if (!response.ok) {
        throw new Error('Failed to mark notification as read')
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, read_status: true }
            : notification
        )
      )
      
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (err) {
      console.error('Error marking notification as read:', err)
      toast.error('فشل في تحديث حالة الإشعار')
    }
  }, [toast])

  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'PATCH',
      })

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read')
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, read_status: true }))
      )
      setUnreadCount(0)
      
      toast.success('تم تحديد جميع الإشعارات كمقروءة')
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
      toast.error('فشل في تحديث الإشعارات')
    }
  }, [toast])

  const refreshNotifications = useCallback(async () => {
    await fetchNotifications()
  }, [fetchNotifications])

  // Initial fetch
  useEffect(() => {
    fetchNotifications()
  }, [fetchNotifications])

  // Set up real-time subscription
  useEffect(() => {
    let notificationChannel: RealtimeChannel | null = null
    let readChannel: RealtimeChannel | null = null

    const setupRealtimeSubscription = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) return

        // Subscribe to new notifications
        notificationChannel = supabase
          .channel('notifications')
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'notifications',
            },
            (payload) => {
              console.log('New notification received:', payload)
              const newNotification = payload.new as Record<string, unknown>

              // Check if this notification is for the current user
              if (newNotification.recipient_id === user.id || newNotification.recipient_id === null) {
                const notificationWithReadStatus: Notification = {
                  ...(newNotification as unknown as Notification),
                  read_status: false,
                  read_at: null
                }

                setNotifications(prev => [notificationWithReadStatus, ...prev.slice(0, limit - 1)])
                setUnreadCount(prev => prev + 1)

                // Show toast for new notification
                toast.success(newNotification.title as string, {
                  description: newNotification.message as string
                })
              }
            }
          )
          .subscribe()

        // Subscribe to read status changes
        readChannel = supabase
          .channel('notification_reads')
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'notification_reads',
              filter: `user_id=eq.${user.id}`,
            },
            (payload) => {
              console.log('Read status change received:', payload)

              if (payload.eventType === 'INSERT') {
                const readRecord = payload.new as Record<string, unknown>
                setNotifications(prev =>
                  prev.map(notification =>
                    notification.id === readRecord.notification_id
                      ? { ...notification, read_status: true, read_at: readRecord.read_at as string | null }
                      : notification
                  )
                )
                setUnreadCount(prev => Math.max(0, prev - 1))
              }
            }
          )
          .subscribe()
      } catch (error) {
        console.error('Error setting up real-time subscription:', error)
      }
    }

    setupRealtimeSubscription()

    return () => {
      if (notificationChannel) {
        supabase.removeChannel(notificationChannel)
      }
      if (readChannel) {
        supabase.removeChannel(readChannel)
      }
    }
  }, [limit, supabase, toast])

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refreshNotifications,
  }
}
